// Test the formatModelName function from Footer component
import fs from 'fs';

// Read the Footer.tsx file and extract the formatModelName function
const footerContent = fs.readFileSync('./packages/cli/src/ui/components/Footer.tsx', 'utf8');

// Extract the function using regex
const functionMatch = footerContent.match(/function formatModelName\(model: string\): string \{[\s\S]*?\n\}/);

if (functionMatch) {
  // Convert TypeScript to JavaScript for testing
  const jsFunction = functionMatch[0]
    .replace('function formatModelName(model: string): string', 'function formatModelName(model)')
    .replace(/: string/g, '');
  
  // Create a test environment
  eval(jsFunction);
  
  console.log('Testing formatModelName function...');
  console.log('deepseek-chat:', formatModelName('deepseek-chat')); // Should be "DeepSeek Chat"
  console.log('deepseek-reasoner:', formatModelName('deepseek-reasoner')); // Should be "DeepSeek Reasoner"
  console.log('gemini-1.5-pro:', formatModelName('gemini-1.5-pro')); // Should be "gemini-1.5-pro"
  console.log('unknown-model:', formatModelName('unknown-model')); // Should be "unknown-model"
  console.log('All tests completed!');
} else {
  console.error('Could not find formatModelName function in Footer.tsx');
}
